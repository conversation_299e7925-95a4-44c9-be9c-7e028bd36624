package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;


import java.util.Optional;

public interface StoreSalesConfigDao extends JpaRepository<StoreSalesConfigEntity, Long> {

    /**
     * 根据门店ID查询配置
     *
     * @param storeId 门店ID
     * @return 门店销售配置
     */
    StoreSalesConfigEntity findByStoreIdAndDeleted(Long storeId, Integer deleted);

    /**
     * 检查门店配置是否存在
     *
     * @param storeId 门店ID
     * @return 是否存在
     */
    boolean existsByStoreIdAndDeleted(Long storeId, Integer deleted);

    @Transactional
    @Modifying
    @Query("""
    UPDATE StoreSalesConfigEntity SET
        seeHouseEnabled = :#{#entity.seeHouseEnabled},
        seeHouseDate = :#{#entity.seeHouseDate},
        seeHouseWeekDay = :#{#entity.seeHouseWeekDay},
        seeHouseTimeStart = :#{#entity.seeHouseTimeStart},
        seeHouseTimeEnd = :#{#entity.seeHouseTimeEnd},
        timeEnabled = :#{#entity.timeEnabled},
        seeHouseTodayEnabled = :#{#entity.seeHouseTodayEnabled},
        seeHouseValidDay = :#{#entity.seeHouseValidDay},
        version = version + 1
    WHERE storeId = :#{#entity.storeId}
    """)
    void update(StoreSalesConfigEntity entity);
}
