package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.sales.domain.reserve.model.SeeHouseStatus;
import com.inboyu.sales.infrastructure.builder.ReserveSeeHouseBuilder;
import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReserveSeeHouseBuilderImpl implements ReserveSeeHouseBuilder {

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public ReserveSeeHouse toReserveSeeHouse(ReserveSeeHouseEntity reserveSeeHouseEntity) {
        return ReserveSeeHouse.builder()
                .id(reserveSeeHouseEntity.getId())
                .reserveSeeHouseId(reserveSeeHouseEntity.getReserveSeeHouseId())
                .customerId(reserveSeeHouseEntity.getCustomerId())
                .storeId(reserveSeeHouseEntity.getStoreId())
                .roomTypeId(reserveSeeHouseEntity.getRoomTypeId())
                .roomId(reserveSeeHouseEntity.getRoomId())
                .reserveDate(reserveSeeHouseEntity.getReserveDate())
                .startTime(reserveSeeHouseEntity.getStartTime())
                .endTime(reserveSeeHouseEntity.getEndTime())
                .staffId(reserveSeeHouseEntity.getStaffId())
                .seeState(reserveSeeHouseEntity.getSeeState())
                // 补全基础字段
                .createTime(reserveSeeHouseEntity.getCreateTime())
                .modifyTime(reserveSeeHouseEntity.getModifyTime())
                .version(reserveSeeHouseEntity.getVersion())
                .deleted(reserveSeeHouseEntity.getDeleted())
                .build();
    }

    @Override
    public ReserveSeeHouseEntity toReserveSeeHouseEntity(ReserveSeeHouse reserveSeeHouse) {
        ReserveSeeHouseEntity entity = new ReserveSeeHouseEntity();
        entity.setId(reserveSeeHouse.getId());
        entity.setCustomerId(reserveSeeHouse.getCustomerId());
        entity.setStoreId(reserveSeeHouse.getStoreId());
        entity.setRoomTypeId(reserveSeeHouse.getRoomTypeId());
        entity.setRoomId(reserveSeeHouse.getRoomId());
        entity.setReserveDate(reserveSeeHouse.getReserveDate());
        entity.setStartTime(reserveSeeHouse.getStartTime());
        entity.setEndTime(reserveSeeHouse.getEndTime());
        entity.setStaffId(reserveSeeHouse.getStaffId());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        entity.setSeeState(reserveSeeHouse.getSeeState());

        // 补全基础字段
        entity.setCreateTime(reserveSeeHouse.getCreateTime());
        entity.setModifyTime(reserveSeeHouse.getModifyTime());
        entity.setVersion(reserveSeeHouse.getVersion());
        entity.setDeleted(reserveSeeHouse.getDeleted() != null ? reserveSeeHouse.getDeleted() : DeleteFlag.NOT_DELETED);

        // 新建时设置默认值
        if (reserveSeeHouse.getReserveSeeHouseId() == null) {
            entity.setReserveSeeHouseId(snowflakeIdGenerator.nextId());
            entity.setSeeState(SeeHouseStatus.BOOKED.getCode());
        }else {
            entity.setReserveSeeHouseId(reserveSeeHouse.getReserveSeeHouseId());
        }
        return entity;
    }
}
