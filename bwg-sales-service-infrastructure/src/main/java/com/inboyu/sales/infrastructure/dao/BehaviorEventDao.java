package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.BehaviorEventEntity;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 行为事件DAO
 */
public interface BehaviorEventDao extends JpaRepository<BehaviorEventEntity, Long> {

    /**
     * 根据行为事件ID查询
     *
     * @param behaviorEventId 行为事件ID
     * @param deleted 删除标志
     * @return 行为事件实体
     */
    BehaviorEventEntity findByBehaviorEventIdAndDeleted(Long behaviorEventId, Integer deleted);
}
