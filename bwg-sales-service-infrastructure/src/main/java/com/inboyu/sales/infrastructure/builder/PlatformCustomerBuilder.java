package com.inboyu.sales.infrastructure.builder;

import com.inboyu.sales.domain.customer.model.PlatformCustomer;
import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;

/**
 * 平台客户构建器
 */
public interface PlatformCustomerBuilder {

    /**
     * 实体转领域模型
     *
     * @param entity 平台客户实体
     * @return 平台客户领域模型
     */
    PlatformCustomer toPlatformCustomer(PlatformCustomerEntity entity);

    /**
     * 领域模型转实体
     *
     * @param platformCustomer 平台客户领域模型
     * @return 平台客户实体
     */
    PlatformCustomerEntity toPlatformCustomerEntity(PlatformCustomer platformCustomer);
}
