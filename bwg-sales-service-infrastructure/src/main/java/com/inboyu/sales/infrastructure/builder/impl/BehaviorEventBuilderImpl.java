package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.behavior.model.BehaviorEvent;
import com.inboyu.sales.infrastructure.builder.BehaviorEventBuilder;
import com.inboyu.sales.infrastructure.dao.entity.BehaviorEventEntity;
import org.springframework.stereotype.Component;

/**
 * 行为事件构建器实现
 */
@Component
public class BehaviorEventBuilderImpl implements BehaviorEventBuilder {

    @Override
    public BehaviorEvent toBehaviorEvent(BehaviorEventEntity entity) {
        if (entity == null) {
            return null;
        }

        return BehaviorEvent.builder()
                .id(entity.getId())
                .behaviorEventId(entity.getBehaviorEventId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion())
                .deleted(entity.getDeleted())
                .eventType(entity.getEventType())
                .eventContent(entity.getEventContent())
                .businessId(entity.getBusinessId())
                .customerId(entity.getCustomerId())
                .storeId(entity.getStoreId())
                .staffId(entity.getStaffId())
                .build();
    }

    @Override
    public BehaviorEventEntity toBehaviorEventEntity(BehaviorEvent behaviorEvent) {
        if (behaviorEvent == null) {
            return null;
        }

        BehaviorEventEntity entity = new BehaviorEventEntity();
        entity.setId(behaviorEvent.getId());
        entity.setBehaviorEventId(behaviorEvent.getBehaviorEventId());
        entity.setCreateTime(behaviorEvent.getCreateTime());
        entity.setModifyTime(behaviorEvent.getModifyTime());
        entity.setVersion(behaviorEvent.getVersion());
        entity.setDeleted(behaviorEvent.getDeleted());
        entity.setEventType(behaviorEvent.getEventType());
        entity.setEventContent(behaviorEvent.getEventContent());
        entity.setBusinessId(behaviorEvent.getBusinessId());
        entity.setCustomerId(behaviorEvent.getCustomerId());
        entity.setStoreId(behaviorEvent.getStoreId());
        entity.setStaffId(behaviorEvent.getStaffId());
        return entity;
    }
}
