package com.inboyu.sales.infrastructure.builder;

import com.inboyu.sales.domain.behavior.model.BehaviorEvent;
import com.inboyu.sales.infrastructure.dao.entity.BehaviorEventEntity;

/**
 * 行为事件构建器
 */
public interface BehaviorEventBuilder {

    /**
     * 实体转领域模型
     *
     * @param entity 行为事件实体
     * @return 行为事件领域模型
     */
    BehaviorEvent toBehaviorEvent(BehaviorEventEntity entity);

    /**
     * 领域模型转实体
     *
     * @param behaviorEvent 行为事件领域模型
     * @return 行为事件实体
     */
    BehaviorEventEntity toBehaviorEventEntity(BehaviorEvent behaviorEvent);
}
