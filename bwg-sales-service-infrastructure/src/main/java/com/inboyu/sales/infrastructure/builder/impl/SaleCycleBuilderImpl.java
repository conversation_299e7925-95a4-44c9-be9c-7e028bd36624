package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.cycle.model.SaleCycle;
import com.inboyu.sales.infrastructure.builder.SaleCycleBuilder;
import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import org.springframework.stereotype.Component;

/**
 * 销售周期构建器实现
 */
@Component
public class SaleCycleBuilderImpl implements SaleCycleBuilder {

    @Override
    public SaleCycle toSaleCycle(SaleCycleEntity entity) {
        if (entity == null) {
            return null;
        }

        return SaleCycle.builder()
                .id(entity.getId())
                .saleCycleId(entity.getSaleCycleId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion())
                .deleted(entity.getDeleted())
                .platformCustomerId(entity.getPlatformCustomerId())
                .platformSaleCycleId(entity.getPlatformSaleCycleId())
                .startTime(entity.getStartTime())
                .endTime(entity.getEndTime())
                .timeOut(entity.getTimeOut())
                .staffId(entity.getStaffId())
                .storeId(entity.getStoreId())
                .customerId(entity.getCustomerId())
                .cycleStatus(entity.getCycleStatus())
                .build();
    }

    @Override
    public SaleCycleEntity toSaleCycleEntity(SaleCycle saleCycle) {
        if (saleCycle == null) {
            return null;
        }

        SaleCycleEntity entity = new SaleCycleEntity();
        entity.setId(saleCycle.getId());
        entity.setSaleCycleId(saleCycle.getSaleCycleId());
        entity.setCreateTime(saleCycle.getCreateTime());
        entity.setModifyTime(saleCycle.getModifyTime());
        entity.setVersion(saleCycle.getVersion());
        entity.setDeleted(saleCycle.getDeleted());
        entity.setPlatformCustomerId(saleCycle.getPlatformCustomerId());
        entity.setPlatformSaleCycleId(saleCycle.getPlatformSaleCycleId());
        entity.setStartTime(saleCycle.getStartTime());
        entity.setEndTime(saleCycle.getEndTime());
        entity.setTimeOut(saleCycle.getTimeOut());
        entity.setStaffId(saleCycle.getStaffId());
        entity.setStoreId(saleCycle.getStoreId());
        entity.setCustomerId(saleCycle.getCustomerId());
        entity.setCycleStatus(saleCycle.getCycleStatus());
        return entity;
    }
}
