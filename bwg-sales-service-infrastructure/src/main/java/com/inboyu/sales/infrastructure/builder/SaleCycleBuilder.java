package com.inboyu.sales.infrastructure.builder;

import com.inboyu.sales.domain.cycle.model.SaleCycle;
import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;

/**
 * 销售周期构建器
 */
public interface SaleCycleBuilder {

    /**
     * 实体转领域模型
     *
     * @param entity 销售周期实体
     * @return 销售周期领域模型
     */
    SaleCycle toSaleCycle(SaleCycleEntity entity);

    /**
     * 领域模型转实体
     *
     * @param saleCycle 销售周期领域模型
     * @return 销售周期实体
     */
    SaleCycleEntity toSaleCycleEntity(SaleCycle saleCycle);
}
