package com.inboyu.sales.infrastructure.dao.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;

/**
 * 平台客户实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_platform_customer")
@Comment("平台客户表")
public class PlatformCustomerEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Comment("自增ID，无业务含义")
    private Long id;

    @Column(name = "platform_customer_id", nullable = false)
    @Comment("业务id")
    private Long platformCustomerId;

    @Column(name = "customer_id", nullable = false)
    @Comment("客户id")
    private Long customerId;

    @Column(name = "name", nullable = false)
    @Comment("客户名称")
    private String name;

    @Column(name = "staff_id", nullable = false)
    @Comment("员工ID")
    private Long staffId;

    @Column(name = "current_sale_cycle_id", nullable = false)
    @Comment("当前销售周期id")
    private Long currentSaleCycleId;

    @Column(name = "customer_stage", nullable = false, length = 200)
    @Comment("字典维护；客户生命周期阶段")
    private String customerStage;
}
