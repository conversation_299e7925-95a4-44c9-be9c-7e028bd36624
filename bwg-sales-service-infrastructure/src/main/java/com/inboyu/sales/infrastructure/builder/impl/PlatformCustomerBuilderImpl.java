package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.sales.domain.customer.model.PlatformCustomer;
import com.inboyu.sales.infrastructure.builder.PlatformCustomerBuilder;
import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;
import org.springframework.stereotype.Component;

/**
 * 平台客户构建器实现
 */
@Component
public class PlatformCustomerBuilderImpl implements PlatformCustomerBuilder {

    @Override
    public PlatformCustomer toPlatformCustomer(PlatformCustomerEntity entity) {
        if (entity == null) {
            return null;
        }

        return PlatformCustomer.builder()
                .id(entity.getId())
                .platformCustomerId(entity.getPlatformCustomerId())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(entity.getVersion())
                .deleted(entity.getDeleted())
                .customerId(entity.getCustomerId())
                .name(entity.getName())
                .staffId(entity.getStaffId())
                .currentSaleCycleId(entity.getCurrentSaleCycleId())
                .customerStage(entity.getCustomerStage())
                .build();
    }

    @Override
    public PlatformCustomerEntity toPlatformCustomerEntity(PlatformCustomer platformCustomer) {
        if (platformCustomer == null) {
            return null;
        }

        PlatformCustomerEntity entity = new PlatformCustomerEntity();
        entity.setId(platformCustomer.getId());
        entity.setPlatformCustomerId(platformCustomer.getPlatformCustomerId());
        entity.setCreateTime(platformCustomer.getCreateTime());
        entity.setModifyTime(platformCustomer.getModifyTime());
        entity.setVersion(platformCustomer.getVersion());
        entity.setDeleted(platformCustomer.getDeleted());
        entity.setCustomerId(platformCustomer.getCustomerId());
        entity.setName(platformCustomer.getName());
        entity.setStaffId(platformCustomer.getStaffId());
        entity.setCurrentSaleCycleId(platformCustomer.getCurrentSaleCycleId());
        entity.setCustomerStage(platformCustomer.getCustomerStage());
        return entity;
    }
}
