package com.inboyu.sales.infrastructure.dao.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;

import java.time.LocalDateTime;

/**
 * 销售周期实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_sale_cycle")
@Comment("销售周期表")
public class SaleCycleEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Comment("自增ID，无业务含义")
    private Long id;

    @Column(name = "sale_cycle_id", nullable = false)
    @Comment("业务ID")
    private Long saleCycleId;

    @Column(name = "platform_customer_id", nullable = false)
    @Comment("平台销售客户id")
    private Long platformCustomerId;

    @Column(name = "platform_sale_cycle_id", nullable = false)
    @Comment("平台销售周期id")
    private Long platformSaleCycleId;

    @Column(name = "start_time", nullable = false)
    @Comment("销售周期开始时间")
    private LocalDateTime startTime;

    @Column(name = "end_time", nullable = false)
    @Comment("销售周期结束时间")
    private LocalDateTime endTime;

    @Column(name = "time_out", nullable = false)
    @Comment("销售周期回收时间")
    private LocalDateTime timeOut;

    @Column(name = "staff_id", nullable = false)
    @Comment("员工ID")
    private Long staffId;

    @Column(name = "store_id", nullable = false)
    @Comment("门店ID")
    private Long storeId;

    @Column(name = "customer_id", nullable = false)
    @Comment("用户ID")
    private Long customerId;

    @Column(name = "cycle_status", nullable = false, length = 100)
    @Comment("字典维护，销售周期状态")
    private String cycleStatus;
}
