package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.PlatformCustomerEntity;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 平台客户DAO
 */
public interface PlatformCustomerDao extends JpaRepository<PlatformCustomerEntity, Long> {

    /**
     * 根据平台客户ID查询
     *
     * @param platformCustomerId 平台客户ID
     * @param deleted 删除标志
     * @return 平台客户实体
     */
    PlatformCustomerEntity findByPlatformCustomerIdAndDeleted(Long platformCustomerId, Integer deleted);
}
