package com.inboyu.sales.infrastructure.builder.impl;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.store.model.*;
import com.inboyu.sales.infrastructure.builder.StoreSalesConfigBuilder;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigBuilderImpl implements StoreSalesConfigBuilder {

    @Override
    public StoreSalesConfigEntity toStoreSalesConfigEntity(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }

        StoreSalesConfigEntity entity = new StoreSalesConfigEntity();
//        entity.setId(storeSalesConfig.getId());
//        entity.setCreateTime(storeSalesConfig.getCreateTime());
//        entity.setModifyTime(storeSalesConfig.getModifyTime());
//        entity.setVersion(storeSalesConfig.getVersion());
//        entity.setDeleted(storeSalesConfig.getDeleted());
        entity.setStoreId(storeSalesConfig.getStoreId().getValue());
        entity.setSeeHouseEnabled(storeSalesConfig.getSeeHouseEnabled().getCode());
        entity.setSeeHouseDate(storeSalesConfig.getSeeHouseDate());
        entity.setSeeHouseWeekDay(storeSalesConfig.getSeeHouseWeekDay().getCode());
        entity.setSeeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart());
        entity.setSeeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd());
        entity.setTimeEnabled(storeSalesConfig.getTimeEnabled().getCode());
        entity.setSeeHouseTodayEnabled(storeSalesConfig.getSeeHouseTodayEnabled().getCode());
        entity.setSeeHouseValidDay(storeSalesConfig.getSeeHouseValidDay().getCode());
        entity.setDeleted(DeleteFlag.NOT_DELETED);

        return entity;
    }

    @Override
    public StoreSalesConfig toStoreSalesConfig(StoreSalesConfigEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return StoreSalesConfig.builder()

//                .createTime(entity.getCreateTime())
//                .modifyTime(entity.getModifyTime())
//                .deleted(entity.getDeleted())
//                .version(entity.getVersion())
                .storeId(StoreId.of(entity.getStoreId()))
                .seeHouseEnabled(SeeHouseEnabled.builder()
                        .code(entity.getSeeHouseEnabled())
                        .build())
                .seeHouseDate(entity.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder()
                        .code(entity.getSeeHouseWeekDay())
                        .build())
                .seeHouseTimeStart(entity.getSeeHouseTimeStart())
                .seeHouseTimeEnd(entity.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder()
                        .code(entity.getTimeEnabled())
                        .build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder()
                        .code(entity.getSeeHouseTodayEnabled())
                        .build())
                .seeHouseValidDay(SeeHouseValidDay.builder()
                        .code(entity.getSeeHouseValidDay())
                        .build())
                .build();
    }
}
