package com.inboyu.sales.infrastructure.repository;

import com.inboyu.constant.DeleteFlag;
import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.repository.StoreSalesConfigRepository;
import com.inboyu.sales.infrastructure.builder.StoreSalesConfigBuilder;
import com.inboyu.sales.infrastructure.dao.StoreSalesConfigDao;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


@Repository
public class StoreSalesConfigRepositoryImpl implements StoreSalesConfigRepository {

    @Autowired
    private StoreSalesConfigDao storeSalesConfigDao;

    @Autowired
    private StoreSalesConfigBuilder storeSalesConfigBuilder;

    @Override
    public StoreSalesConfig save(StoreSalesConfig storeSalesConfig) {
        StoreSalesConfigEntity entity = storeSalesConfigBuilder.toStoreSalesConfigEntity(storeSalesConfig);
        StoreSalesConfigEntity savedEntity = storeSalesConfigDao.save(entity);

        return storeSalesConfigBuilder.toStoreSalesConfig(savedEntity);
    }



    @Override
    public StoreSalesConfig findByStoreId(StoreId storeId) {
        StoreSalesConfigEntity entity = storeSalesConfigDao.findByStoreIdAndDeleted(storeId.getValue(), DeleteFlag.NOT_DELETED);
        return storeSalesConfigBuilder.toStoreSalesConfig(entity);
    }

    @Override
    public void update(StoreSalesConfig storeSalesConfig) {
        StoreSalesConfigEntity storeSalesConfigEntity = storeSalesConfigBuilder.toStoreSalesConfigEntity(storeSalesConfig);
        storeSalesConfigDao.update(storeSalesConfigEntity);
    }

    @Override
    public boolean existsByStoreId(StoreId storeId) {
        return storeSalesConfigDao.existsByStoreIdAndDeleted(storeId.getValue(), DeleteFlag.NOT_DELETED);
    }
}
