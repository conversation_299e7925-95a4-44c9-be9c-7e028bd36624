package com.inboyu.sales.infrastructure.dao.entity;

import jakarta.persistence.*;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_reserve_see_house")
@Comment("预约看房表")
public class ReserveSeeHouseEntity extends BaseEntity {

    @Column(name = "id")
    @Comment("自增ID，无业务含义")
    private Long id;

    @Column(name = "customer_id", nullable = false)
    @Comment("客户ID")
    private Long customerId;

    @Column(name = "staff_id", nullable = false)
    @Comment("员工ID")
    private Long staffId;

    @Column(name = "store_id", nullable = false)
    @Comment("门店ID")
    private Long storeId;

    @Column(name = "room_type_id", nullable = false)
    @Comment("户型ID")
    private Long roomTypeId;

    @Column(name = "room_id", nullable = false)
    @Comment("房间ID")
    private Long roomId;

    @Column(name = "reserve_date", nullable = false)
    @Comment("预约日期")
    private LocalDate reserveDate;

    @Column(name = "start_time", nullable = false)
    @Comment("开始时间")
    private LocalTime startTime;

    @Column(name = "end_time", nullable = false)
    @Comment("结束时间")
    private LocalTime endTime;

}