package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.ReserveSeeHouseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ReserveSeeHouseDao extends JpaRepository<ReserveSeeHouseEntity, Long> {

    /**
     * 分页查询预约看房
     *
     * @param storeId    门店ID
     * @param customerId 客户ID
     * @param pageable   分页参数
     * @return {@link Page }<{@link ReserveSeeHouseEntity }>
     */
    @Query("SELECT r FROM ReserveSeeHouseEntity r WHERE r.deleted = 0 AND (:storeId IS NULL OR r.storeId = :storeId) AND (:customerId IS NULL OR r.customerId = :customerId) ORDER BY r.createTime DESC")
    Page<ReserveSeeHouseEntity> pageByCustomerIdAndStoreId(Long customerId, Long storeId, Pageable pageable);


    /**
     *
     * @param reserveSeeHouseId
     * @param deleted
     * @return
     */
    ReserveSeeHouseEntity findByReserveSeeHouseIdAndDeleted(Long reserveSeeHouseId, int deleted);


}