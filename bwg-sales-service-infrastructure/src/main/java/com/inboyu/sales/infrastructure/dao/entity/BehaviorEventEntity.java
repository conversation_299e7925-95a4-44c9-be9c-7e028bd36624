package com.inboyu.sales.infrastructure.dao.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;

/**
 * 行为事件实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_behavior_event")
@Comment("行为事件表")
public class BehaviorEventEntity extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Comment("自增ID，无业务含义")
    private Long id;

    @Column(name = "behavior_event_id", nullable = false)
    @Comment("行为事件业务ID")
    private Long behaviorEventId;

    @Column(name = "event_type", nullable = false, length = 200)
    @Comment("行为事件类型")
    private String eventType;

    @Column(name = "event_content", nullable = false, length = 500)
    @Comment("事件内容")
    private String eventContent;

    @Column(name = "business_id", nullable = false)
    @Comment("对应业务表的id")
    private Long businessId;

    @Column(name = "customer_id", nullable = false)
    @Comment("客户ID")
    private Long customerId;

    @Column(name = "store_id", nullable = false)
    @Comment("门店ID")
    private Long storeId;

    @Column(name = "staff_id", nullable = false)
    @Comment("员工ID")
    private Long staffId;
}
