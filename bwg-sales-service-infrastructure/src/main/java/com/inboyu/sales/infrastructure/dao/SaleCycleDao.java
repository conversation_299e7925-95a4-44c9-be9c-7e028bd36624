package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 销售周期DAO
 */
public interface SaleCycleDao extends JpaRepository<SaleCycleEntity, Long> {

    /**
     * 根据销售周期ID查询
     *
     * @param saleCycleId 销售周期ID
     * @param deleted 删除标志
     * @return 销售周期实体
     */
    SaleCycleEntity findBySaleCycleIdAndDeleted(Long saleCycleId, Integer deleted);

    /**
     * 分页查询门店客户列表（按销售周期创建时间降序，客户去重）
     *
     * @param storeId 门店ID（可选）
     * @param pageable 分页参数
     * @return 分页的销售周期列表
     */
    @Query(value = """
        SELECT sc.* FROM t_sale_cycle sc
        INNER JOIN (
            SELECT customer_id, MAX(create_time) as max_create_time
            FROM t_sale_cycle
            WHERE deleted = 0
            AND (:storeId IS NULL OR store_id = :storeId)
            GROUP BY customer_id
        ) latest ON sc.customer_id = latest.customer_id
        AND sc.create_time = latest.max_create_time
        WHERE sc.deleted = 0
        AND (:storeId IS NULL OR sc.store_id = :storeId)
        ORDER BY sc.create_time DESC
        """,
        countQuery = """
        SELECT COUNT(DISTINCT sc.customer_id) FROM t_sale_cycle sc
        WHERE sc.deleted = 0
        AND (:storeId IS NULL OR sc.store_id = :storeId)
        """,
        nativeQuery = true)
    Page<SaleCycleEntity> findCustomerListByStore(@Param("storeId") Long storeId, Pageable pageable);
}
