package com.inboyu.sales.infrastructure.dao;

import com.inboyu.sales.infrastructure.dao.entity.SaleCycleEntity;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 销售周期DAO
 */
public interface SaleCycleDao extends JpaRepository<SaleCycleEntity, Long> {

    /**
     * 根据销售周期ID查询
     *
     * @param saleCycleId 销售周期ID
     * @param deleted 删除标志
     * @return 销售周期实体
     */
    SaleCycleEntity findBySaleCycleIdAndDeleted(Long saleCycleId, Integer deleted);
}
