package com.inboyu.sales.infrastructure.builder;

import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.infrastructure.dao.entity.StoreSalesConfigEntity;

public interface StoreSalesConfigBuilder {
    
    /**
     * 将实体转换为领域模型
     *
     * @param entity 实体对象
     * @return 领域模型
     */
    StoreSalesConfig toStoreSalesConfig(StoreSalesConfigEntity entity);

    /**
     * 将领域模型转换为实体
     *
     * @param storeSalesConfig 领域模型
     * @return 实体对象
     */
    StoreSalesConfigEntity toStoreSalesConfigEntity(StoreSalesConfig storeSalesConfig);
}
