server:
  port: 10171
  #  默认为IMMEDIATE，表示立即关机；GRACEFUL表示优雅关机
  shutdown: graceful
management:
  endpoint:
    health:
      probes:
        enabled: true
logging:
  config: ${LOGBACK_CONFIG_PATH:}
spring:
  profiles:
    active: dev
  threads:
    virtual:
      enabled: true
  # 停机过程超时时长设置了20s，超过20s，直接停机
  lifecycle:
    timeout-per-shutdown-phase: 20s
  application:
    name: sales-service-app
  datasource: # 数据库驱动
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************_${spring.profiles.active}_sales?allowMultiQueries=true  # 数据库连接地址
    username: bwg_op
    password: bwg@123
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  jpa:
    show-sql: true
  data:
    redis:
      host: tenant-system-redis
      port: 6379
      password: 1eFvYJ4adRlT1D12Q2YmIPm77Spi6euH
      database: 0
      lettuce:
        pool:
          max-idle: 10
          min-idle: 10
          max-active: 20
          max-wait: 1000
  cache:
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 86400000  # 1天
      # 缓存null值，防止缓存穿透
      cache-null-values: true
  jackson:
    deserialization:
      WRAP_EXCEPTIONS: false
redis:
  server:
    key-prefix: ${spring.application.name} # redis key 前缀
    enable-snow-flake-id: true #是否启用redis雪花id
    data-center-id: 2 #平台用1，商户用2
xxl:
  job:
    enabled: true
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: ${spring.application.name}
      ip: ${xxjoblocalip:} #本机注册到xxjob的地址”IP:PORT“ 作为注册地址，通过系统环境变量传入，如果不传则默认内部ip
      port: ${xxjoblocalport:40099} #本机注册到xxjob的地址”IP:PORT“ 作为注册地址，通过系统环境变量传入，如果不传则默认内部ip
      logpath: ./logs/xxjob #执行器运行日志文件存储磁盘路径 [选填]
      logretentiondays: 15  #执行器日志文件保存天数 [选填] ： 过期日志自动清理
    accessToken: a9e199aa-0266-cdbe-8ded-dea2e51ac0f1
application:
  app-id: 10252
  app-key: 7c0JXAHbu5vmmW2r
  app-name: 销售服务跑批
biz:
  jpa: ##脱敏加密秘钥
    encrypt-key: 6EKo9%+Cq^yKexDD
    #是否启用多租户hint
    enable-tenant:  true
    #数据库名
    dbName: bwg_${spring.profiles.active}_sales
    #数据库前缀 组合： dev_admin_tenantId 为具体数据库到hint
    #prefix: ${spring.profiles.active}
