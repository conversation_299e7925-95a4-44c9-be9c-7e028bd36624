package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Value
@Builder
public class SeeHouseWeekDay {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY = "store_sales_config_see_house_week_day.Monday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_TUESDAY = "store_sales_config_see_house_week_day.Tuesday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_WEDNESDAY = "store_sales_config_see_house_week_day.Wednesday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_THURSDAY = "store_sales_config_see_house_week_day.Thursday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_FRIDAY = "store_sales_config_see_house_week_day.Friday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SATURDAY = "store_sales_config_see_house_week_day.Saturday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SUNDAY = "store_sales_config_see_house_week_day.Sunday";
}
