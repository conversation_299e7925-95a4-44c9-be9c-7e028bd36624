package com.inboyu.sales.domain.customer.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 客户生命周期阶段值对象
 */
@Getter
@EqualsAndHashCode
public class CustomerStage {

    // 客户阶段常量
    public static final CustomerStage POTENTIAL = new CustomerStage("platform_customer_customer_stage.potential", "潜在");
    public static final CustomerStage INTENTION = new CustomerStage("platform_customer_customer_stage.intention", "意向");
    public static final CustomerStage RENTED = new CustomerStage("platform_customer_customer_stage.rented", "租住");

    private final String code;
    private final String description;

    private CustomerStage(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CustomerStage fromCode(String code) {
        return switch (code) {
            case "platform_customer_customer_stage.potential" -> POTENTIAL;
            case "platform_customer_customer_stage.intention" -> INTENTION;
            case "platform_customer_customer_stage.rented" -> RENTED;
            default -> throw new IllegalArgumentException("Unknown customer stage code: " + code);
        };
    }

    @Override
    public String toString() {
        return code;
    }
}
