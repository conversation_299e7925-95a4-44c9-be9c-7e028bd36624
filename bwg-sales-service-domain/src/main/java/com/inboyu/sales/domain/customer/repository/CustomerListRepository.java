package com.inboyu.sales.domain.customer.repository;

import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.stereotype.Repository;

/**
 * 客户列表仓储接口
 */
@Repository
public interface CustomerListRepository {

    /**
     * 分页查询客户列表
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param storeId  门店ID（可选）
     * @return 分页的客户列表
     */
    Pagination<CustomerListItem> pageCustomerList(Integer pageNum, Integer pageSize, Long storeId);
}
