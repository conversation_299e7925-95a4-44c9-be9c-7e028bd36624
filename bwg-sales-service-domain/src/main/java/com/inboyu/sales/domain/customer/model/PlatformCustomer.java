package com.inboyu.sales.domain.customer.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 平台客户聚合根
 */
@Getter
@Setter
@Builder
public class PlatformCustomer {

    /**
     * 自增ID，无业务含义
     */
    private Long id;

    /**
     * 平台客户业务ID
     */
    private Long platformCustomerId;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 记录版本号（乐观锁）
     */
    private Long version;

    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String name;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 当前销售周期ID
     */
    private Long currentSaleCycleId;

    /**
     * 客户生命周期阶段
     */
    private String customerStage;

    /**
     * 获取客户阶段
     */
    public CustomerStage getCustomerStageEnum() {
        return CustomerStage.fromCode(this.customerStage);
    }

    /**
     * 设置客户阶段
     */
    public void setCustomerStageEnum(CustomerStage customerStage) {
        this.customerStage = customerStage.getCode();
    }
}
