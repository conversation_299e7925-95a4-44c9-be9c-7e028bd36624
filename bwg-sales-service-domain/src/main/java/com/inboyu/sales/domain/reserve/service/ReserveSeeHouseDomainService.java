package com.inboyu.sales.domain.reserve.service;

import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;

public interface ReserveSeeHouseDomainService {
    /**
     * 分页查询预约看房
     *
     * @param pageNum    当前页
     * @param pageSize   每页条数
     * @param customerId 客户ID（可选）
     * @param storeId    门店ID（可选）
     * @return {@link Pagination}<{@link ReserveSeeHouse}>
     */
    Pagination<ReserveSeeHouse> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId);

    /**
     * 保存预约看房
     *
     * @param reserveSeeHouse 预约看房领域模型
     * @return {@link ReserveSeeHouse}
     */
    ReserveSeeHouse save(ReserveSeeHouse reserveSeeHouse);


    /**
     * 根据ID查询预约看房
     *
     * @param id 预约ID
     * @return {@link ReserveSeeHouse}
     */
    ReserveSeeHouse findById(Long id);

}
