package com.inboyu.sales.domain.customer.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 平台客户ID值对象
 */
@Getter
@EqualsAndHashCode
public class PlatformCustomerId {

    private final Long value;

    private PlatformCustomerId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("平台客户ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static PlatformCustomerId of(Long value) {
        return new PlatformCustomerId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
