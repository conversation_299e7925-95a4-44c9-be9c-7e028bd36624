package com.inboyu.sales.domain.customer.service;

import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

/**
 * 客户列表领域服务接口
 */
public interface CustomerListDomainService {

    /**
     * 分页查询客户列表
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param storeId  门店ID（可选）
     * @return 分页的客户列表
     */
    Pagination<CustomerListItem> pageCustomerList(Integer pageNum, Integer pageSize, Long storeId);
}
