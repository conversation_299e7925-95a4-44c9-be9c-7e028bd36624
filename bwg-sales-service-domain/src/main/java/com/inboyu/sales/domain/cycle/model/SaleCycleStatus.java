package com.inboyu.sales.domain.cycle.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 销售周期状态值对象
 */
@Getter
@EqualsAndHashCode
public class SaleCycleStatus {

    // 销售周期状态常量
    public static final SaleCycleStatus ACTIVE = new SaleCycleStatus("sale_cycle_cycle_status.active", "激活");
    public static final SaleCycleStatus CLOSED = new SaleCycleStatus("sale_cycle_cycle_status.closed", "关闭");
    public static final SaleCycleStatus SIGNED = new SaleCycleStatus("sale_cycle_cycle_status.signed", "签约");

    private final String code;
    private final String description;

    private SaleCycleStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SaleCycleStatus fromCode(String code) {
        return switch (code) {
            case "sale_cycle_cycle_status.active" -> ACTIVE;
            case "sale_cycle_cycle_status.closed" -> CLOSED;
            case "sale_cycle_cycle_status.signed" -> SIGNED;
            default -> throw new IllegalArgumentException("Unknown sale cycle status code: " + code);
        };
    }

    @Override
    public String toString() {
        return code;
    }
}
