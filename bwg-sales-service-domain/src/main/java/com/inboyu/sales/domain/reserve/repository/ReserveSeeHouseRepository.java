package com.inboyu.sales.domain.reserve.repository;

import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.stereotype.Repository;

@Repository
public interface ReserveSeeHouseRepository {

    /**
     * 分页查询预约看房
     *
     * @param pageNum    当前页
     * @param pageSize   每页条数
     * @param customerId 客户ID（可选）
     * @param storeId    门店ID（可选）
     * @return {@link Pagination}<{@link ReserveSeeHouse}>
     */
    Pagination<ReserveSeeHouse> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId);

    /**
     * 保存预约看房记录
     *
     * @param reserveSeeHouse 预约看房实体
     * @return 保存后的实体
     */
    ReserveSeeHouse save(ReserveSeeHouse reserveSeeHouse);


    /**
     * 根据ID查询预约看房记录
     *
     * @param ReserveSeeHouseId
     * @return
     */
    ReserveSeeHouse findByReserveSeeHouseId(Long ReserveSeeHouseId);
}
