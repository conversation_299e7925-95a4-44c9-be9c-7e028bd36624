package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 客户ID值对象
 */
@Getter
@EqualsAndHashCode
public class CustomerId {

    private final Long value;

    private CustomerId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("客户ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static CustomerId of(Long value) {
        return new CustomerId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
