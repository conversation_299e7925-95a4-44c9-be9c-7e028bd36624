package com.inboyu.sales.domain.customer.service.impl;

import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.repository.CustomerListRepository;
import com.inboyu.sales.domain.customer.service.CustomerListDomainService;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户列表领域服务实现
 */
@Service
public class CustomerListDomainServiceImpl implements CustomerListDomainService {

    @Autowired
    private CustomerListRepository customerListRepository;

    @Override
    public Pagination<CustomerListItem> pageCustomerList(Integer pageNum, Integer pageSize, Long storeId) {
        
        // 参数校验
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100; // 限制最大页面大小
        }
        
        return customerListRepository.pageCustomerList(pageNum, pageSize, storeId);
    }
}
