package com.inboyu.sales.domain.reserve.service.impl;

import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.sales.domain.reserve.repository.ReserveSeeHouseRepository;
import com.inboyu.sales.domain.reserve.service.ReserveSeeHouseDomainService;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReserveSeeHouseDomainServiceImpl implements ReserveSeeHouseDomainService {

    @Autowired
    private ReserveSeeHouseRepository reserveSeeHouseRepository;

    @Override
    public Pagination<ReserveSeeHouse> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId) {
        return reserveSeeHouseRepository.pageReserveSeeHouse(pageNum, pageSize, customerId, storeId);
    }

    @Override
    public ReserveSeeHouse save(ReserveSeeHouse reserveSeeHouse) {
        return reserveSeeHouseRepository.save(reserveSeeHouse);
    }

    @Override
    public ReserveSeeHouse findById(Long reserveSeeHouseId) {
        return reserveSeeHouseRepository.findByReserveSeeHouseId(reserveSeeHouseId);
    }
}
