package com.inboyu.sales.domain.behavior.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 行为事件聚合根
 */
@Getter
@Setter
@Builder
public class BehaviorEvent {

    /**
     * 自增ID，无业务含义
     */
    private Long id;

    /**
     * 行为事件业务ID
     */
    private Long behaviorEventId;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 记录版本号（乐观锁）
     */
    private Long version;

    /**
     * 是否删除，0否，1是
     */
    private Integer deleted;

    /**
     * 行为事件类型
     */
    private String eventType;

    /**
     * 事件内容
     */
    private String eventContent;

    /**
     * 对应业务表的id
     */
    private Long businessId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 员工ID
     */
    private Long staffId;
}
