package com.inboyu.sales.domain.store.model;


import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Value
public class StoreId {
    Long value;
    public StoreId(Long value) {
        if (null == value) {
            throw new IllegalArgumentException("门店id非法");
        }
        this.value = value;
    }

    public static StoreId of(Long value) {
        return new StoreId(value);
    }
}
