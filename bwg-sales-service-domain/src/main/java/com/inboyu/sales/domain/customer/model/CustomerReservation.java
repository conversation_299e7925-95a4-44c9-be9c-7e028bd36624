package com.inboyu.sales.domain.customer.model;

import com.inboyu.sales.domain.reserve.model.SeeHouseStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 客户预约记录领域模型
 */
@Getter
@Setter
@Builder
public class CustomerReservation {

    /**
     * 预约日期
     */
    private LocalDate reserveDate;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 约看状态
     */
    private String seeState;

    /**
     * 获取约看状态枚举
     */
    public SeeHouseStatus getSeeHouseStatus() {
        return SeeHouseStatus.fromCode(this.seeState);
    }

    /**
     * 获取格式化的时间显示 MM-DD HH:mm
     */
    public String getFormattedTime() {
        if (reserveDate == null || startTime == null) {
            return "";
        }
        return reserveDate.format(DateTimeFormatter.ofPattern("MM-dd")) + " " +
               startTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    /**
     * 获取格式化的完整显示 MM-DD HH:mm(状态)
     */
    public String getFormattedDisplay() {
        String timeStr = getFormattedTime();
        if (timeStr.isEmpty()) {
            return "";
        }
        
        SeeHouseStatus status = getSeeHouseStatus();
        return timeStr + "(" + status.getDescription() + ")";
    }
}
