package com.inboyu.sales.domain.customer.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 客户列表项领域模型
 */
@Getter
@Setter
@Builder
public class CustomerListItem {

    /**
     * 平台客户ID
     */
    private Long platformCustomerId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 当前销售周期ID
     */
    private Long currentSaleCycleId;

    /**
     * 销售周期创建时间
     */
    private LocalDateTime saleCycleCreateTime;

    /**
     * 客户生命周期阶段
     */
    private String customerStage;

    /**
     * 预约看房记录
     */
    private CustomerReservation reservation;
}
