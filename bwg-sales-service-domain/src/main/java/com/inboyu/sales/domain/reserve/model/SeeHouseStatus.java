package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 看房状态值对象
 * 封装预约看房的状态变化业务逻辑
 */
@Getter
@EqualsAndHashCode
public class SeeHouseStatus {

    // 根据需求定义的状态常量
    public static final SeeHouseStatus BOOKED = new SeeHouseStatus("reserve_see_house_see_state.booked", "预约成功");
    public static final SeeHouseStatus CANCELED = new SeeHouseStatus("reserve_see_house_see_state.canceled", "已取消");
    public static final SeeHouseStatus COMPLETED = new SeeHouseStatus("reserve_see_house_see_state.completed", "成功看房");
    public static final SeeHouseStatus OVERDUE = new SeeHouseStatus("reserve_see_house.see_state.overdue", "逾期未看");

    private final String code;
    private final String description;

    private SeeHouseStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SeeHouseStatus of(String code) {
        return switch (code) {
            case "reserve_see_house_see_state.booked" -> BOOKED;
            case "reserve_see_house_see_state.canceled" -> CANCELED;
            case "reserve_see_house_see_state.completed" -> COMPLETED;
            case "reserve_see_house.see_state.overdue" -> OVERDUE;
            default -> throw new IllegalArgumentException("未知的看房状态: " + code);
        };
    }

    /**
     * 检查是否可以转换到目标状态
     */
    public boolean canTransitionTo(SeeHouseStatus targetStatus) {
        return switch (this.code) {
            case "reserve_see_house_see_state.booked" ->
                targetStatus.equals(COMPLETED) || targetStatus.equals(CANCELED) || targetStatus.equals(OVERDUE);
            case "reserve_see_house_see_state.canceled",
                 "reserve_see_house_see_state.completed",
                 "reserve_see_house.see_state.overdue" -> false; // 终态不能再转换
            default -> false;
        };
    }

    /**
     * 检查是否为活动状态（非终态）
     */
    public boolean isActive() {
        return this.equals(BOOKED);
    }

    /**
     * 检查是否为终态
     */
    public boolean isFinalState() {
        return this.equals(COMPLETED) || this.equals(CANCELED) || this.equals(OVERDUE);
    }

    /**
     * 检查是否可以取消
     */
    public boolean canCancel() {
        return this.equals(BOOKED);
    }

    /**
     * 检查是否可以完成
     */
    public boolean canComplete() {
        return this.equals(BOOKED);
    }

    @Override
    public String toString() {
        return code;
    }

    public static SeeHouseStatus fromCode(String code) {
        return switch (code) {
            case "reserve_see_house_see_state.booked" -> BOOKED;
            case "reserve_see_house_see_state.canceled" -> CANCELED;
            case "reserve_see_house_see_state.completed" -> COMPLETED;
            case "reserve_see_house_see_state.overdue" -> OVERDUE;
            default -> throw new IllegalArgumentException("Unknown status code: " + code);
        };
    }

}
