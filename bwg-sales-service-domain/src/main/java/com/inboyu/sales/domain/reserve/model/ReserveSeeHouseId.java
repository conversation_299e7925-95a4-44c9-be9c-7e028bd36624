package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 预约看房ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@EqualsAndHashCode
public class ReserveSeeHouseId {

    private final Long value;

    private ReserveSeeHouseId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("预约看房ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static ReserveSeeHouseId of(Long value) {
        return new ReserveSeeHouseId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
