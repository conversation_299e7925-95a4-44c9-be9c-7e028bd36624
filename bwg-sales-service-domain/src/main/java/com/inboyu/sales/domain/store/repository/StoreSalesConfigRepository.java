package com.inboyu.sales.domain.store.repository;

import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import org.springframework.stereotype.Repository;

@Repository
public interface StoreSalesConfigRepository {

    /**
     * 保存门店销售配置
     *
     * @param storeSalesConfig 门店销售配置
     * @return 保存后的配置
     */
    StoreSalesConfig save(StoreSalesConfig storeSalesConfig);

    /**
     * 根据门店ID查询配置
     *
     * @param storeId 门店ID
     * @return 门店销售配置
     */
    StoreSalesConfig findByStoreId(StoreId storeId);

    /**
     * 更新门店销售配置
     *
     * @param storeSalesConfig 门店销售配置
     */
    void update(StoreSalesConfig storeSalesConfig);

    /**
     * 检查门店配置是否存在
     *
     * @param storeId 门店ID
     * @return 是否存在
     */
    boolean existsByStoreId(StoreId storeId);
}
