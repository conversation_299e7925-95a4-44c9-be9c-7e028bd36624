package com.inboyu.sales.domain.store.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2025/9/3
 */
@Getter
@Setter
@Builder
public class StoreSalesConfig {

    /**
     * 门店ID
     */
    private final StoreId storeId;

    /**
     * 是否开放预约看房，字典维护
     */
    private SeeHouseEnabled seeHouseEnabled;

    /**
     * 预约看房开放日期
     */
    private LocalDate seeHouseDate;

    /**
     * 预约看房可选星期，字典维护
     */
    private SeeHouseWeekDay seeHouseWeekDay;

    /**
     * 预约起始时间
     */
    private LocalTime seeHouseTimeStart;

    /**
     * 预约截止时间
     */
    private LocalTime seeHouseTimeEnd;

    /**
     * 是否开放预约具体时间，字典维护
     */
    private TimeEnabled timeEnabled;

    /**
     * 是否开放当天预约，字典维护
     */
    private SeeHouseTodayEnabled seeHouseTodayEnabled;

    /**
     * 可预约最大时间范围，字典维护
     */
    private SeeHouseValidDay seeHouseValidDay;
}
