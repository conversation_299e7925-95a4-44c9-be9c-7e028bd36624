package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 户型ID值对象
 */
@Getter
@EqualsAndHashCode
public class RoomTypeId {

    private final Long value;

    private RoomTypeId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("户型ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static RoomTypeId of(Long value) {
        return new RoomTypeId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
