package com.inboyu.sales.domain.store.service.impl;

import com.inboyu.sales.domain.store.model.StoreId;
import com.inboyu.sales.domain.store.model.StoreSalesConfig;
import com.inboyu.sales.domain.store.repository.StoreSalesConfigRepository;
import com.inboyu.sales.domain.store.service.StoreSalesConfigDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StoreSalesConfigDomainServiceImpl implements StoreSalesConfigDomainService {

    @Autowired
    private StoreSalesConfigRepository storeSalesConfigRepository;

    @Override
    public StoreSalesConfig createStoreSalesConfig(StoreSalesConfig storeSalesConfig) {
        // 业务规则验证：检查门店是否已有配置
        if (storeSalesConfigRepository.existsByStoreId(storeSalesConfig.getStoreId())) {
            throw new IllegalArgumentException("门店配置已存在，请使用更新操作");
        }
        
        // 业务规则验证：预约时间范围检查
        if (storeSalesConfig.getSeeHouseTimeStart() != null && 
            storeSalesConfig.getSeeHouseTimeEnd() != null &&
            storeSalesConfig.getSeeHouseTimeStart().isAfter(storeSalesConfig.getSeeHouseTimeEnd())) {
            throw new IllegalArgumentException("预约开始时间不能晚于结束时间");
        }
        
        return storeSalesConfigRepository.save(storeSalesConfig);
    }

    @Override
    public StoreSalesConfig getStoreSalesConfigByStoreId(StoreId storeId) {
        return storeSalesConfigRepository.findByStoreId(storeId);
    }

    @Override
    public void updateStoreSalesConfig(StoreSalesConfig storeSalesConfig) {

        StoreSalesConfig existingStoreSalesConfig = storeSalesConfigRepository.findByStoreId(storeSalesConfig.getStoreId());

        // 业务规则验证：配置必须存在
        if (null == existingStoreSalesConfig) {
            throw new IllegalArgumentException("门店对应销售配置不存在，无法更新");
        }

        // 业务规则验证：预约时间范围检查
        if (storeSalesConfig.getSeeHouseTimeStart() != null && 
            storeSalesConfig.getSeeHouseTimeEnd() != null &&
            storeSalesConfig.getSeeHouseTimeStart().isAfter(storeSalesConfig.getSeeHouseTimeEnd())) {
            throw new IllegalArgumentException("预约开始时间不能晚于结束时间");
        }

        storeSalesConfigRepository.update(storeSalesConfig);
    }
}
