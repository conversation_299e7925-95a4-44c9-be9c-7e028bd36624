package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 门店ID值对象
 */
@Getter
@EqualsAndHashCode
public class StoreId {

    private final Long value;

    private StoreId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("门店ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static StoreId of(Long value) {
        return new StoreId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
