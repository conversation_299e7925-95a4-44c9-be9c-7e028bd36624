package com.inboyu.sales.domain.reserve.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 房间ID值对象
 */
@Getter
@EqualsAndHashCode
public class RoomId {

    private final Long value;

    private RoomId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("房间ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static RoomId of(Long value) {
        return new RoomId(value);
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
