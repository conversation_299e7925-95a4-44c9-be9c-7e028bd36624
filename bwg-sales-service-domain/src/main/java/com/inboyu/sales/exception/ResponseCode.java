package com.inboyu.sales.exception;

import com.inboyu.spring.cloud.starter.common.constant.ResponseCodeInterface;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年07月29日 20:04
 */
public enum ResponseCode implements ResponseCodeInterface {

    /** 通用响应码 */
    SUCCESS(0, "成功"),

    /** 销售服务错误码范围 16000 ～ 16999*/
    RESERVE_SEE_HOUSE_NOT_FOUND(16001, "预约看房记录不存在"),
    RESERVE_SEE_HOUSE_CANNOT_CANCEL(16002, "当前状态不允许取消预约"),
    RESERVE_SEE_HOUSE_ALREADY_CANCELED(16003, "预约已取消"),
    RESERVE_SEE_HOUSE_ALREADY_COMPLETED(16004, "预约已完成"),
    RESERVE_SEE_HOUSE_OVERDUE(16005, "预约已逾期"),

    /** 平台客户相关错误码 */
    PLATFORM_CUSTOMER_NOT_FOUND(16101, "平台客户不存在"),
    PLATFORM_CUSTOMER_ALREADY_EXISTS(16102, "平台客户已存在")
    ;

    /** 销售服务错误码范围 16000 ～ 16999*/
    private final int code;
    private final String msg;

    ResponseCode(int code, String des) {
        this.code = code;
        this.msg = des;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
