package com.inboyu.sales.app.integration;

import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.app.application.CustomerListAppService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 客户列表集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
class CustomerListIntegrationTest {

    @Autowired
    private CustomerListAppService customerListAppService;

    @Test
    void testPageCustomerList_Integration() {
        // Given
        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(1)
                .pageSize(10)
                .storeId(null) // 查询所有门店
                .build();

        // When
        CustomerListResponseDTO response = customerListAppService.pageCustomerList(request);

        // Then
        assertNotNull(response);
        assertNotNull(response.getCustomerList());
        assertTrue(response.getCustomerList().getPageNum() >= 1);
        assertTrue(response.getCustomerList().getPageSize() >= 1);
        assertTrue(response.getCustomerList().getTotal() >= 0);
    }

    @Test
    void testPageCustomerList_WithStoreFilter() {
        // Given
        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(1)
                .pageSize(5)
                .storeId(1001L)
                .build();

        // When
        CustomerListResponseDTO response = customerListAppService.pageCustomerList(request);

        // Then
        assertNotNull(response);
        assertNotNull(response.getCustomerList());
        assertEquals(1, response.getCustomerList().getPageNum());
        assertEquals(5, response.getCustomerList().getPageSize());
    }
}
