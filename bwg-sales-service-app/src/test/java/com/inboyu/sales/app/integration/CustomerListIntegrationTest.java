package com.inboyu.sales.app.integration;

import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.app.application.CustomerListAppService;
import com.inboyu.sales.domain.customer.service.CustomerListService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 客户列表集成测试
 */
@SpringBootTest(classes = {CustomerListAppService.class, CustomerListService.class})
@ActiveProfiles("test")
class CustomerListIntegrationTest {

    @Autowired
    private CustomerListAppService customerListAppService;

    @Test
    void testPageCustomerList_WithRequiredStoreId() {
        // Given
        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(1)
                .pageSize(10)
                .storeId(1001L) // 必须指定门店ID
                .build();

        // When
        CustomerListResponseDTO response = customerListAppService.pageCustomerList(request);

        // Then
        assertNotNull(response);
        assertNotNull(response.getCustomerList());
        assertTrue(response.getCustomerList().getPageNum() >= 1);
        assertTrue(response.getCustomerList().getPageSize() >= 1);
        assertTrue(response.getCustomerList().getTotal() >= 0);

        // 验证返回的客户都属于指定门店
        if (!response.getCustomerList().getList().isEmpty()) {
            response.getCustomerList().getList().forEach(customer -> {
                assertEquals(1001L, customer.getStoreId());
            });
        }
    }

    @Test
    void testPageCustomerList_WithNullStoreId_ShouldThrowException() {
        // Given
        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(1)
                .pageSize(5)
                .storeId(null) // 门店ID为空应该抛出异常
                .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            customerListAppService.pageCustomerList(request);
        });
    }

    @Test
    void testPageCustomerList_WithDifferentPageSizes() {
        // Given
        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(1)
                .pageSize(5)
                .storeId(1001L)
                .build();

        // When
        CustomerListResponseDTO response = customerListAppService.pageCustomerList(request);

        // Then
        assertNotNull(response);
        assertNotNull(response.getCustomerList());
        assertEquals(1, response.getCustomerList().getPageNum());
        assertEquals(5, response.getCustomerList().getPageSize());
    }
}
