package com.inboyu.sales.app.dto.request.store;

import com.inboyu.sales.app.dto.common.store.*;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新门店销售配置请求")
public class UpdateStoreSalesConfigRequestDTO {

    @NotNull(message = "门店ID不能为空")
    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String storeId;

    @NotNull(message = "是否开放预约看房不能为空")
    @Valid
    @Schema(description = "是否开放预约看房", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seeHouseEnabled;

    @NotNull(message = "预约看房开放日期不能为空")
    @Schema(description = "预约看房开放日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate seeHouseDate;

    @NotNull(message = "预约看房可选星期不能为空")
    @Valid
    @Schema(description = "预约看房可选星期，字典维护", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seeHouseWeekDay;

    @NotNull(message = "预约起始时间不能为空")
    @Schema(description = "预约起始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalTime seeHouseTimeStart;

    @NotNull(message = "预约截止时间不能为空")
    @Schema(description = "预约截止时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalTime seeHouseTimeEnd;

    @NotNull(message = "是否开放预约具体时间不能为空")
    @Valid
    @Schema(description = "是否开放预约具体时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String timeEnabled;

    @NotNull(message = "是否开放当天预约不能为空")
    @Valid
    @Schema(description = "是否开放当天预约", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seeHouseTodayEnabled;

    @NotNull(message = "可预约最大时间范围不能为空")
    @Valid
    @Schema(description = "可预约最大时间范围", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seeHouseValidDay;
}
