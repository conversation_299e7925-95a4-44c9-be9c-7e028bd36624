package com.inboyu.sales.app.dto.common.store;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预约具体时间开放状态DTO
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预约具体时间开放状态")
public class TimeEnabledDTO {
    
    @Schema(description = "状态编码")
    private String code;

    @Schema(description = "状态值")
    private String title;
    
    // 常量定义
    public static final String STATUS_ENABLED = "status.enabled";
    public static final String STATUS_DISABLED = "status.disabled";
}
