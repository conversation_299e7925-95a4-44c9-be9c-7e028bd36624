package com.inboyu.sales.app.dto.common.store;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 当天预约开放状态DTO
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "当天预约开放状态")
public class SeeHouseTodayEnabledDTO {

    /**
     * 状态编码
     */
    @Schema(description = "状态编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态编码不能为空")
    private String code;
    /**
     * 状态值
     */
    @Schema(description = "状态值")
    private String title;
}
