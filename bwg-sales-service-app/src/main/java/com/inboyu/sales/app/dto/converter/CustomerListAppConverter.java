package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.response.customer.CustomerCardDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerReservationDTO;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.model.CustomerReservation;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户列表应用层转换器
 */
@Component
public class CustomerListAppConverter {

    /**
     * 领域模型转换为DTO
     */
    public CustomerListResponseDTO toResponseDTO(Pagination<CustomerListItem> domainPagination) {
        List<CustomerCardDTO> customerCards = domainPagination.getList().stream()
                .map(this::toCustomerCardDTO)
                .collect(Collectors.toList());

        Pagination<CustomerCardDTO> customerCardPagination = new Pagination<>(
                domainPagination.getPageNum(),
                domainPagination.getPageSize(),
                domainPagination.getTotalPages(),
                domainPagination.getTotal(),
                customerCards);

        return CustomerListResponseDTO.builder()
                .customerList(customerCardPagination)
                .build();
    }

    /**
     * 客户列表项转换为客户卡片DTO
     */
    private CustomerCardDTO toCustomerCardDTO(CustomerListItem item) {
        return CustomerCardDTO.builder()
                .platformCustomerId(item.getPlatformCustomerId())
                .customerId(item.getCustomerId())
                .customerName(item.getCustomerName())
                .storeId(item.getStoreId())
                .currentSaleCycleId(item.getCurrentSaleCycleId())
                .saleCycleCreateTime(item.getSaleCycleCreateTime())
                .customerStage(item.getCustomerStage())
                .reservation(toReservationDTO(item.getReservation()))
                .build();
    }

    /**
     * 客户预约记录转换为DTO
     */
    private CustomerReservationDTO toReservationDTO(CustomerReservation reservation) {
        if (reservation == null) {
            return null;
        }

        return CustomerReservationDTO.builder()
                .reserveDate(reservation.getReserveDate())
                .startTime(reservation.getStartTime())
                .endTime(reservation.getEndTime())
                .seeStateCode(reservation.getSeeState())
                .seeStateDescription(reservation.getSeeHouseStatus().getDescription())
                .formattedTime(reservation.getFormattedTime())
                .formattedDisplay(reservation.getFormattedDisplay())
                .build();
    }
}
