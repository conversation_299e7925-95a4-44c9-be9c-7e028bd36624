package com.inboyu.sales.app.controller;

import com.inboyu.sales.app.application.CustomerListAppService;
import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 客户列表控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/customer-list")
@Tag(name = "客户列表控制器")
public class CustomerListController {

    @Autowired
    private CustomerListAppService customerListAppService;

    @GetMapping
    @Operation(summary = "分页查询客户列表", description = "支持按门店ID筛选的分页客户列表查询")
    public ResponseEntity<CustomerListResponseDTO> pageCustomerList(
            @RequestParam @Parameter(description = "当前页", example = "1") Integer pageNum,
            @RequestParam @Parameter(description = "每页条数", example = "10") Integer pageSize,
            @RequestParam(required = false) @Parameter(description = "门店ID", example = "1001") Long storeId) {
        
        log.info("查询客户列表，页码：{}，每页条数：{}，门店ID：{}", pageNum, pageSize, storeId);

        CustomerListQueryRequestDTO request = CustomerListQueryRequestDTO.builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .storeId(storeId)
                .build();

        CustomerListResponseDTO response = customerListAppService.pageCustomerList(request);
        
        log.info("客户列表查询完成，返回{}条记录", 
                response.getCustomerList() != null ? response.getCustomerList().getList().size() : 0);

        return ResponseEntity.ok(response);
    }
}
