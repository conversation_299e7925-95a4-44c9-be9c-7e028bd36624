package com.inboyu.sales.app.application;

import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDetailResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

public interface ReserveSeeHouseAppService {

    /**
     * 分页查询预约看房
     *
     * @param pageNum    当前页
     * @param pageSize   每页条数
     * @param customerId 客户ID（可选）
     * @param storeId    门店ID（可选）
     * @return {@link Pagination}<{@link ReserveSeeHouseDTO}>
     */
    Pagination<ReserveSeeHouseDTO> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId);

    /**
     * 新增预约看房
     *
     * @param dto 创建请求DTO
     * @return {@link ReserveSeeHouseCreateResponseDTO}
     */
    ReserveSeeHouseCreateResponseDTO createReserveSeeHouse(ReserveSeeHouseCreateRequestDTO dto);

    /**
     * 获取预约看房详情
     *
     * @param reserveSeeHouseId 预约看房ID
     * @return {@link ReserveSeeHouseDetailResponseDTO}
     */
    ReserveSeeHouseDetailResponseDTO getReserveSeeHouseDetail(Long reserveSeeHouseId);

    /**
     * 取消预约看房
     *
     * @param reserveSeeHouseId 预约看房ID
     * @return {@link ReserveSeeHouseDetailResponseDTO} 取消后的预约详情
     */
    ReserveSeeHouseDetailResponseDTO cancelReserveSeeHouse(Long reserveSeeHouseId);
}