package com.inboyu.sales.app.controller;

import com.inboyu.sales.app.application.ReserveSeeHouseAppService;
import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDetailResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;

@Slf4j
@RestController
@RequestMapping("/api/v1/reserve-see-house")
@Tag(name = "预约看房控制器")
public class ReserveSeeHouseController {

    @Autowired
    private ReserveSeeHouseAppService reserveSeeHouseAppService;

    @GetMapping
    @Operation(summary = "获取预约看房列表")
    public Pagination<ReserveSeeHouseDTO> pageReserveSeeHouse(
            @RequestParam @Schema(description = "当前页") Integer pageNum,
            @RequestParam @Schema(description = "每页条数") Integer pageSize,
            @RequestParam(required = false) @Schema(description = "客户id") String customerId,
            @RequestParam(required = false) @Schema(description = "门店id") String storeId) {
        log.info("获取预约看房列表");
        Long customerIdLong = StringUtils.isNotBlank(customerId) ? Long.valueOf(customerId) : null;
        Long storeIdLong = StringUtils.isNotBlank(storeId) ? Long.valueOf(storeId) : null;
        return reserveSeeHouseAppService.pageReserveSeeHouse(pageNum, pageSize, customerIdLong, storeIdLong);
    }

    @PostMapping
    @Operation(summary = "新增预约看房")
    public ReserveSeeHouseCreateResponseDTO createReserveSeeHouse(@RequestBody ReserveSeeHouseCreateRequestDTO dto) {
        log.info("新增预约看房: {}", dto);
        return reserveSeeHouseAppService.createReserveSeeHouse(dto);
    }

    @GetMapping("/{reserveSeeHouseId}")
    @Operation(summary = "获取预约详情")
    public ReserveSeeHouseDetailResponseDTO getReserveSeeHouseDetail(@PathVariable String reserveSeeHouseId) {
        Long id = StringUtils.isNotBlank(reserveSeeHouseId) ? Long.valueOf(reserveSeeHouseId) : null;
        return reserveSeeHouseAppService.getReserveSeeHouseDetail(id);
    }

    @PostMapping("/{reserveSeeHouseId}/cancel")
    @Operation(summary = "取消预约看房")
    public ReserveSeeHouseDetailResponseDTO cancelReserveSeeHouse(@PathVariable String reserveSeeHouseId) {
        Long id = StringUtils.isNotBlank(reserveSeeHouseId) ? Long.valueOf(reserveSeeHouseId) : null;
        return reserveSeeHouseAppService.cancelReserveSeeHouse(id);
    }

    /*
    @PostMapping("/{id}")
    @Operation(summary = "更新预约看房")
    public String updateReserveSeeHouse(@PathVariable Long id) {
        log.info("更新预约看房，ID: {}", id);
        return "更新预约看房xx";
    }
    */
}
