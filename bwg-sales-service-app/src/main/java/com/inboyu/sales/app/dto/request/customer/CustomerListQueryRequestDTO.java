package com.inboyu.sales.app.dto.request.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户列表查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户列表查询请求")
public class CustomerListQueryRequestDTO {

    @Schema(description = "当前页", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer pageNum;

    @Schema(description = "每页条数", requiredMode = Schema.RequiredMode.REQUIRED, example = "30")
    private Integer pageSize;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "342232908947394560")
    private Long storeId;
}
