package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.ReserveSeeHouseAppService;
import com.inboyu.sales.app.dto.converter.ReserveSeeHouseAppConverter;
import com.inboyu.sales.app.dto.request.reserve.ReserveSeeHouseCreateRequestDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseCreateResponseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDTO;
import com.inboyu.sales.app.dto.response.reserve.ReserveSeeHouseDetailResponseDTO;
import com.inboyu.sales.domain.reserve.model.ReserveSeeHouse;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.sales.domain.reserve.repository.ReserveSeeHouseRepository;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import com.inboyu.sales.exception.ResponseCode;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Log4j2
public class ReserveSeeHouseAppServiceImpl implements ReserveSeeHouseAppService {

    @Autowired
    private ReserveSeeHouseRepository reserveSeeHouseRepository;


    @Autowired
    private ReserveSeeHouseAppConverter reserveSeeHouseAppConverter;


    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public Pagination<ReserveSeeHouseDTO> pageReserveSeeHouse(Integer pageNum, Integer pageSize, Long customerId, Long storeId) {
        Pagination<ReserveSeeHouse> reserveSeeHouses = reserveSeeHouseRepository.pageReserveSeeHouse(pageNum, pageSize, customerId, storeId);
        List<ReserveSeeHouseDTO> storeDTOs = reserveSeeHouses.getList().stream()
                .map(reserveSeeHouseAppConverter::toDTO).toList();
        // 4. 构造分页结果
        return new Pagination<>(
                reserveSeeHouses.getPageNum(),
                reserveSeeHouses.getPageSize(),
                reserveSeeHouses.getTotalPages(),
                reserveSeeHouses.getTotal(),
                storeDTOs);
    }

    @Override
    public ReserveSeeHouseCreateResponseDTO createReserveSeeHouse(ReserveSeeHouseCreateRequestDTO dto) {
        log.info("创建预约看房记录: {}", dto);

        // 2. 将请求 DTO 转换为领域模型
        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseAppConverter.toDomainFromCreateRequest(dto);

        log.info("转换后的预约看房领域模型: {}", reserveSeeHouse);

        // 3. 保存到仓储
        ReserveSeeHouse saved = reserveSeeHouseRepository.save(reserveSeeHouse);

        // 4. 转换为创建响应 DTO 返回
        return reserveSeeHouseAppConverter.toCreateResponseDTO(saved);

    }

    @Override
    public ReserveSeeHouseDetailResponseDTO getReserveSeeHouseDetail(Long reserveSeeHouseId) {
        log.info("查询预约看房详情，ID：{}", reserveSeeHouseId);

        // 使用仓储查询
        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseRepository.findByReserveSeeHouseId(reserveSeeHouseId);

        if (reserveSeeHouse == null) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_NOT_FOUND);
        }

        return reserveSeeHouseAppConverter.toDetailResponseDTO(reserveSeeHouse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReserveSeeHouseDetailResponseDTO cancelReserveSeeHouse(Long reserveSeeHouseId) {
        log.info("取消预约看房，ID：{}", reserveSeeHouseId);

        // 1. 查询预约看房记录
        ReserveSeeHouse reserveSeeHouse = reserveSeeHouseRepository.findByReserveSeeHouseId(reserveSeeHouseId);
        if (reserveSeeHouse == null) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_NOT_FOUND);
        }

        // 2. 检查是否可以取消
        if (!reserveSeeHouse.getStatus().canCancel()) {
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_CANNOT_CANCEL,
                    reserveSeeHouse.getStatus().getDescription());
        }

        try {
            // 3. 使用聚合根的业务方法取消预约
            reserveSeeHouse.cancel();

            // 4. 保存状态变更
            ReserveSeeHouse updated = reserveSeeHouseRepository.save(reserveSeeHouse);

            log.info("预约看房取消成功，ID：{}，状态变更为：{}", reserveSeeHouseId, updated.getStatus().getDescription());

            // 5. 转换为响应DTO返回
            return reserveSeeHouseAppConverter.toDetailResponseDTO(updated);

        } catch (Exception e) {
            log.error("取消预约看房失败，ID：{}，错误信息：{}", reserveSeeHouseId, e.getMessage(), e);
            throw new AppException(ResponseCode.RESERVE_SEE_HOUSE_CANNOT_CANCEL, e.getMessage());
        }
    }
}