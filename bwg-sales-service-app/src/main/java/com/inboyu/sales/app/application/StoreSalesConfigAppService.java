package com.inboyu.sales.app.application;

import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;

public interface StoreSalesConfigAppService {

    /**
     * 创建门店销售配置
     *
     * @param request 创建请求
     * @return 创建后的配置
     */
    StoreSalesConfigDTO createStoreSalesConfig(CreateStoreSalesConfigRequestDTO request);

    /**
     * 根据门店ID查询配置
     *
     * @param storeId 门店ID
     * @return 门店销售配置
     */
    StoreSalesConfigDTO getStoreSalesConfigByStoreId(Long storeId);

    /**
     * 更新门店销售配置
     *
     * @param request 更新请求
     * @return 更新后的配置
     */
    StoreSalesConfigDTO updateStoreSalesConfig(UpdateStoreSalesConfigRequestDTO request);
}
