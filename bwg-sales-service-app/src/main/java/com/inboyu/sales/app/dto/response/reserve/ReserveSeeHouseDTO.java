package com.inboyu.sales.app.dto.response.reserve;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约看房响应DTO
 */
@Data
@Builder
@Schema(description = "预约看房信息")
public class ReserveSeeHouseDTO {

    @Schema(description = "自增ID，无业务含义")
    private Long id;

    @Schema(description = "记录创建时间")
    private LocalDateTime createTime;

    @Schema(description = "记录修改时间")
    private LocalDateTime modifyTime;

    @Schema(description = "记录版本号（乐观锁）")
    private Long version;

    @Schema(description = "是否删除，0否，1是")
    private Integer deleted;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "员工ID")
    private Long staffId;

    @Schema(description = "门店ID")
    private Long storeId;

    @Schema(description = "户型ID")
    private Long roomTypeId;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "预约日期")
    private LocalDate reserveDate;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;
}