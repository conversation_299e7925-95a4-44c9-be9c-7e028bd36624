package com.inboyu.sales.app.dto.converter;

import com.inboyu.sales.app.dto.common.store.*;
import com.inboyu.sales.app.dto.request.store.CreateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.request.store.UpdateStoreSalesConfigRequestDTO;
import com.inboyu.sales.app.dto.response.store.StoreSalesConfigDTO;
import com.inboyu.sales.domain.store.model.*;
import org.springframework.stereotype.Component;

@Component
public class StoreSalesConfigAppConverter {

    /**
     * 将领域模型转换为DTO
     */
    public StoreSalesConfigDTO toStoreSalesConfigDTO(StoreSalesConfig storeSalesConfig) {
        if (storeSalesConfig == null) {
            return null;
        }
        
        return StoreSalesConfigDTO.builder()
//                .id(String.valueOf(storeSalesConfig.getId()))
                .storeId(String.valueOf((storeSalesConfig.getStoreId() != null ? storeSalesConfig.getStoreId().getValue() : null)))
                .seeHouseEnabled(storeSalesConfig.getSeeHouseEnabled().getCode())
                .seeHouseDate(storeSalesConfig.getSeeHouseDate())
                .seeHouseWeekDay(storeSalesConfig.getSeeHouseWeekDay().getCode())
                .seeHouseTimeStart(storeSalesConfig.getSeeHouseTimeStart())
                .seeHouseTimeEnd(storeSalesConfig.getSeeHouseTimeEnd())
                .timeEnabled(storeSalesConfig.getTimeEnabled().getCode())
                .seeHouseTodayEnabled(storeSalesConfig.getSeeHouseTodayEnabled().getCode())
                .seeHouseValidDay(storeSalesConfig.getSeeHouseValidDay().getCode())
                .build();
    }

    /**
     * 将创建请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(CreateStoreSalesConfigRequestDTO createStoreSalesConfigRequestDTO) {
        if (createStoreSalesConfigRequestDTO == null) {
            return null;
        }

        if (createStoreSalesConfigRequestDTO.getStoreId() == null) {
            throw new IllegalArgumentException("门店ID不能为空");
        }

        return StoreSalesConfig.builder()
                .storeId(StoreId.of(Long.valueOf(createStoreSalesConfigRequestDTO.getStoreId())))
                .seeHouseEnabled(SeeHouseEnabled.builder().code(createStoreSalesConfigRequestDTO.getSeeHouseEnabled()).build())
                .seeHouseDate(createStoreSalesConfigRequestDTO.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code(createStoreSalesConfigRequestDTO.getSeeHouseWeekDay()).build())
                .seeHouseTimeStart(createStoreSalesConfigRequestDTO.getSeeHouseTimeStart())
                .seeHouseTimeEnd(createStoreSalesConfigRequestDTO.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder().code(createStoreSalesConfigRequestDTO.getTimeEnabled()).build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code(createStoreSalesConfigRequestDTO.getSeeHouseTodayEnabled()).build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code(createStoreSalesConfigRequestDTO.getSeeHouseValidDay()).build())
                .build();
    }

    /**
     * 将更新请求转换为领域模型
     */
    public StoreSalesConfig toStoreSalesConfig(UpdateStoreSalesConfigRequestDTO updateStoreSalesConfigRequestDTO) {
        if (updateStoreSalesConfigRequestDTO == null) {
            return null;
        }

        if (updateStoreSalesConfigRequestDTO.getStoreId() == null) {
            throw new IllegalArgumentException("门店ID不能为空");
        }

        return StoreSalesConfig.builder()
                .storeId(StoreId.of(Long.valueOf(updateStoreSalesConfigRequestDTO.getStoreId())))
                .seeHouseEnabled(SeeHouseEnabled.builder().code(updateStoreSalesConfigRequestDTO.getSeeHouseEnabled()).build())
                .seeHouseDate(updateStoreSalesConfigRequestDTO.getSeeHouseDate())
                .seeHouseWeekDay(SeeHouseWeekDay.builder().code(updateStoreSalesConfigRequestDTO.getSeeHouseWeekDay()).build())
                .seeHouseTimeStart(updateStoreSalesConfigRequestDTO.getSeeHouseTimeStart())
                .seeHouseTimeEnd(updateStoreSalesConfigRequestDTO.getSeeHouseTimeEnd())
                .timeEnabled(TimeEnabled.builder().code(updateStoreSalesConfigRequestDTO.getTimeEnabled()).build())
                .seeHouseTodayEnabled(SeeHouseTodayEnabled.builder().code(updateStoreSalesConfigRequestDTO.getSeeHouseTodayEnabled()).build())
                .seeHouseValidDay(SeeHouseValidDay.builder().code(updateStoreSalesConfigRequestDTO.getSeeHouseValidDay()).build())
                .build();
    }
}
