package com.inboyu.sales.app.dto.response.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 客户卡片DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户卡片信息")
public class CustomerCardDTO {

    @Schema(description = "平台客户ID")
    private Long platformCustomerId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户名称", example = "张三")
    private String customerName;

    @Schema(description = "门店ID")
    private Long storeId;

    @Schema(description = "当前销售周期ID")
    private Long currentSaleCycleId;

    @Schema(description = "销售周期创建时间")
    private LocalDateTime saleCycleCreateTime;

    @Schema(description = "客户生命周期阶段")
    private String customerStage;

    @Schema(description = "预约看房记录")
    private CustomerReservationDTO reservation;
}
