package com.inboyu.sales.app.application.impl;

import com.inboyu.sales.app.application.CustomerListAppService;
import com.inboyu.sales.app.dto.converter.CustomerListAppConverter;
import com.inboyu.sales.app.dto.request.customer.CustomerListQueryRequestDTO;
import com.inboyu.sales.app.dto.response.customer.CustomerListResponseDTO;
import com.inboyu.sales.domain.customer.model.CustomerListItem;
import com.inboyu.sales.domain.customer.service.CustomerListDomainService;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户列表应用服务实现
 */
@Service
@Slf4j
public class CustomerListAppServiceImpl implements CustomerListAppService {

    @Autowired
    private CustomerListDomainService customerListDomainService;

    @Autowired
    private CustomerListAppConverter customerListAppConverter;

    @Override
    public CustomerListResponseDTO pageCustomerList(CustomerListQueryRequestDTO request) {
        log.info("应用层查询客户列表，请求参数：{}", request);

        // 调用领域服务查询
        Pagination<CustomerListItem> domainResult = customerListDomainService.pageCustomerList(
                request.getPageNum(),
                request.getPageSize(),
                request.getStoreId());

        // 转换为响应DTO
        return customerListAppConverter.toResponseDTO(domainResult);
    }
}
