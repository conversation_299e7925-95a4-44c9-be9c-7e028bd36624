package com.inboyu.sales.app.dto.common.store;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 预约看房可选星期DTO
 * 
 * <AUTHOR>
 * @date 2025/9/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "预约看房可选星期")
public class SeeHouseWeekDayDTO {
    
    @Schema(description = "星期编码")
    private String code;

    @Schema(description = "星期值")
    private String title;
    
    // 常量定义
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_MONDAY = "store_sales_config_see_house_week_day.Monday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_TUESDAY = "store_sales_config_see_house_week_day.Tuesday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_WEDNESDAY = "store_sales_config_see_house_week_day.Wednesday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_THURSDAY = "store_sales_config_see_house_week_day.Thursday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_FRIDAY = "store_sales_config_see_house_week_day.Friday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SATURDAY = "store_sales_config_see_house_week_day.Saturday";
    public static final String STORE_SALES_CONFIG_SEE_HOUSE_WEEK_DAY_SUNDAY = "store_sales_config_see_house_week_day.Sunday";
}
