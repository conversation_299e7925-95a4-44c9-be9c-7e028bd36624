package com.inboyu.sales.app.dto.response.customer;

import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户列表响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户列表响应")
public class CustomerListResponseDTO {

    @Schema(description = "分页客户列表")
    private Pagination<CustomerCardDTO> customerList;
}
