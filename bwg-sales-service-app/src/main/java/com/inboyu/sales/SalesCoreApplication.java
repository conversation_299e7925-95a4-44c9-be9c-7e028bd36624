package com.inboyu.sales;

import com.inboyu.spring.cloud.starter.web.EnableWeb;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableFeignClients(basePackages = "com.inboyu")
@ComponentScan(basePackages = {"com.inboyu"})
@SpringBootApplication
@EnableWeb
public class SalesCoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(SalesCoreApplication.class, args);
    }

}
