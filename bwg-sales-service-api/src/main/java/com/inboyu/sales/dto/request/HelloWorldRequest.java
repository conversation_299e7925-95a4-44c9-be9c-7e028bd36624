package com.inboyu.sales.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025年07月22日 16:26
 */
public class HelloWorldRequest {
    @Schema(description = "请求参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "参数不能为空")
    private String params;

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }
}
