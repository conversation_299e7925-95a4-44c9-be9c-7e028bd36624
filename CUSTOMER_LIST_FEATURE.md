# 客户列表查询功能实现

## 功能概述

根据DDD架构和分层设计规范，实现了客户列表查询功能，查询指定门店的分页客户列表，并展示客户的预约看房记录。所有ID字段都封装为值对象，确保类型安全。

## 功能特性

### 一、查询指定门店客户列表
1. **筛选条件**：必须指定门店ID（store_id）
2. **数据来源**：指定门店中创建过客户销售周期（SaleCycleEntity）的客户（需去重）
3. **排序**：客户销售周期的创建时间降序，若存在多个销售周期则查最新的
4. **分页支持**：完整的分页功能
5. **值对象封装**：所有ID字段都封装为值对象，确保类型安全

### 二、客户卡片显示内容
1. **用户名**：取平台客户池用户名（PlatformCustomerEntity的name）
2. **预约看房记录**：查取客户在该门店的预约看房记录（ReserveSeeHouseEntity）
   - 优先展示当前销售周期中状态为"成功预约"的记录，取约看结束时间最近的一条
   - 如无成功预约记录，则展示最近一条约看记录
   - 显示格式：MM-DD HH:mm(状态描述)

## 技术实现

### 架构分层

```
Controller Layer (控制器层)
├── CustomerListController - REST API接口

Application Layer (应用层)
├── CustomerListAppService - 应用服务接口
├── CustomerListAppServiceImpl - 应用服务实现
└── CustomerListAppConverter - DTO转换器

Domain Layer (领域层)
├── CustomerListItem - 客户列表项聚合根（使用值对象）
├── CustomerReservation - 客户预约记录值对象
├── PlatformCustomerId - 平台客户ID值对象
├── SaleCycleId - 销售周期ID值对象
├── CustomerId - 客户ID值对象（已存在）
├── StoreId - 门店ID值对象（已存在）
├── CustomerListDomainService - 领域服务
└── CustomerListRepository - 仓储接口

Infrastructure Layer (基础设施层)
├── CustomerListRepositoryImpl - 仓储实现
├── SaleCycleDao - 销售周期DAO（扩展）
└── ReserveSeeHouseDao - 预约看房DAO（扩展）
```

### 核心查询逻辑

1. **主查询SQL**：使用窗口函数实现客户去重和排序
```sql
SELECT sc.* FROM t_sale_cycle sc
INNER JOIN (
    SELECT customer_id, MAX(create_time) as max_create_time
    FROM t_sale_cycle 
    WHERE deleted = 0 AND (:storeId IS NULL OR store_id = :storeId)
    GROUP BY customer_id
) latest ON sc.customer_id = latest.customer_id 
AND sc.create_time = latest.max_create_time
WHERE sc.deleted = 0 AND (:storeId IS NULL OR sc.store_id = :storeId)
ORDER BY sc.create_time DESC
```

2. **预约记录查询**：优先级排序查询
```sql
SELECT r FROM ReserveSeeHouseEntity r 
WHERE r.deleted = 0 AND r.customerId = :customerId 
AND r.storeId = :storeId AND r.createTime >= :saleCycleCreateTime
ORDER BY 
    CASE WHEN r.seeState = 'reserve_see_house_see_state.booked' THEN 0 ELSE 1 END,
    r.endTime DESC, r.createTime DESC
```

## API接口

### 客户列表查询

**接口地址**：`GET /api/v1/customer-list`

**请求参数**：
- `pageNum` (必填): 当前页码，从1开始
- `pageSize` (必填): 每页条数，最大100
- `storeId` (必填): 门店ID，必须指定

**响应示例**：
```json
{
  "customerList": {
    "pageNum": 1,
    "pageSize": 10,
    "totalPages": 5,
    "total": 50,
    "list": [
      {
        "platformCustomerId": 12345,
        "customerId": 67890,
        "customerName": "张三",
        "storeId": 1001,
        "currentSaleCycleId": 54321,
        "saleCycleCreateTime": "2025-09-08T10:30:00",
        "customerStage": "potential",
        "reservation": {
          "reserveDate": "2025-09-08",
          "startTime": "09:00",
          "endTime": "10:00",
          "seeStateCode": "reserve_see_house_see_state.booked",
          "seeStateDescription": "成功预约",
          "formattedTime": "09-08 09:00",
          "formattedDisplay": "09-08 09:00(成功预约)"
        }
      }
    ]
  }
}
```

## 测试覆盖

### 集成测试
- `CustomerListIntegrationTest` - 端到端集成测试，包含值对象验证和门店ID必填验证

## 使用示例

### 查询指定门店的客户列表
```bash
curl -X GET "http://localhost:8080/api/v1/customer-list?pageNum=1&pageSize=10&storeId=1001"
```

### 门店ID为必填参数
```bash
# 以下请求会返回400错误，因为缺少storeId参数
curl -X GET "http://localhost:8080/api/v1/customer-list?pageNum=1&pageSize=10"
```

## 注意事项

1. **性能优化**：使用了数据库层面的去重和排序，避免了应用层的大量数据处理
2. **数据一致性**：通过销售周期创建时间来筛选对应的预约记录，确保数据关联正确
3. **类型安全**：所有ID字段都封装为值对象，避免了ID类型混淆的问题
4. **参数校验**：门店ID为必填参数，确保查询的准确性
5. **错误处理**：包含了完整的参数校验和异常处理
6. **扩展性**：遵循DDD架构，便于后续功能扩展和维护

## 依赖关系

本功能依赖以下现有组件：
- `SeeHouseStatus` - 预约状态枚举
- `PlatformCustomerEntity` - 平台客户实体
- `SaleCycleEntity` - 销售周期实体
- `ReserveSeeHouseEntity` - 预约看房实体
- `Pagination` - 分页工具类
